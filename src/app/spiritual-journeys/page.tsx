"use client";

import React from "react";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { Phone, MapPin, Globe, Calendar, Users, Car, Clock, CheckCircle } from "lucide-react";

export default function SpiritualJourneysPage() {
  return (
    <div className="min-h-screen flex flex-col bg-black-950">
      <Header />

      {/* Main content wrapper */}
      <div className="flex-grow flex flex-col">
        {/* Hero Banner */}
        <section className="py-16 mt-16 md:py-20 bg-black-600 bg-meditation cosmic-bg relative">
          <div className="absolute inset-0 z-0">
            <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-black-950 to-transparent" />
            <div className="absolute bottom-0 left-0 w-full h-64 bg-gradient-to-t from-black-950 to-transparent" />
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center animate-fade-in">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                <span className="text-white">Sacred </span>
                <span className="text-gradient">Kashmir</span>
              </h1>
              <p className="text-lg text-white mb-8">
                A transformative 15-day spiritual journey through the mystical landscapes of Kashmir
              </p>
            </div>
          </div>
        </section>

        {/* Contact Information */}
        <section className="py-8 bg-black-800">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div className="flex items-center justify-center space-x-2 text-white">
                <Phone className="w-5 h-5 text-gold-500" />
                <div>
                  <p className="text-sm text-white/60">Call Us</p>
                  <p className="font-medium">1800 889 0856 | +91-7006 527 131</p>
                </div>
              </div>
              <div className="flex items-center justify-center space-x-2 text-white">
                <MapPin className="w-5 h-5 text-gold-500" />
                <div>
                  <p className="text-sm text-white/60">Reach Us</p>
                  <p className="font-medium">1st Floor, Pulloo Complex, Munawarabad, Srinagar</p>
                </div>
              </div>
              <div className="flex items-center justify-center space-x-2 text-white">
                <Globe className="w-5 h-5 text-gold-500" />
                <div>
                  <p className="text-sm text-white/60">Website</p>
                  <p className="font-medium">www.themultidestinations.com</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Tour Overview */}
        <section className="py-12 bg-black-700">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-8 text-center">
                Tour <span className="text-gradient">Overview</span>
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <div className="flex items-center space-x-3 mb-3">
                    <Users className="w-6 h-6 text-gold-500" />
                    <h3 className="text-white font-semibold">Traveler</h3>
                  </div>
                  <p className="text-white/80">Mr. Andrew Harvey</p>
                </div>

                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <div className="flex items-center space-x-3 mb-3">
                    <Clock className="w-6 h-6 text-gold-500" />
                    <h3 className="text-white font-semibold">Duration</h3>
                  </div>
                  <p className="text-white/80">15 Days 14 Nights</p>
                </div>

                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <div className="flex items-center space-x-3 mb-3">
                    <MapPin className="w-6 h-6 text-gold-500" />
                    <h3 className="text-white font-semibold">Destinations</h3>
                  </div>
                  <p className="text-white/80">Srinagar, Anantnag, Ganderbal</p>
                </div>

                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <div className="flex items-center space-x-3 mb-3">
                    <Car className="w-6 h-6 text-gold-500" />
                    <h3 className="text-white font-semibold">Transportation</h3>
                  </div>
                  <p className="text-white/80">Force Urbania + Toyota Fortuner</p>
                </div>

                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <div className="flex items-center space-x-3 mb-3">
                    <Users className="w-6 h-6 text-gold-500" />
                    <h3 className="text-white font-semibold">Group Size</h3>
                  </div>
                  <p className="text-white/80">15 Adults</p>
                </div>

                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <div className="flex items-center space-x-3 mb-3">
                    <Calendar className="w-6 h-6 text-gold-500" />
                    <h3 className="text-white font-semibold">Expected Arrival</h3>
                  </div>
                  <p className="text-white/80">May 2026</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Inclusions */}
        <section className="py-12 bg-black-800">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-8 text-center">
                Tour <span className="text-gradient">Inclusions</span>
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  "14 Nights at Designated Cultural Resorts (privately booked)",
                  "Breakfast, Lunch & Dinner",
                  "English Speaking Tour Guide",
                  "Shikara Ride in Dal Lake",
                  "Transfers & Sightseeing",
                  "Pilgrim visits to Designated Temples, Ashram & Shrines",
                  "Sufi Music Night",
                  "Arts & Handicrafts Exhibition",
                  "Spiritual Healer",
                  "Meditation & Satsang",
                  "Trek & Camp to Samkaropala",
                  "Wazwan"
                ].map((inclusion, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-gold-500 mt-0.5 flex-shrink-0" />
                    <p className="text-white/80">{inclusion}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Accommodation */}
        <section className="py-12 bg-black-700">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-8 text-center">
                <span className="text-gradient">Accommodation</span>
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-white font-semibold text-xl mb-3">Srinagar - 12 Nights</h3>
                  <p className="text-gold-500 font-medium">MAQAM</p>
                </div>
                
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-white font-semibold text-xl mb-3">Pahalgam - 1 Night</h3>
                  <p className="text-gold-500 font-medium">SOJOURN COTTAGES</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Detailed Itinerary */}
        <section className="py-12 bg-black-800">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-8 text-center">
                Detailed <span className="text-gradient">Itinerary</span>
              </h2>

              <div className="space-y-8">
                {/* Day 1 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 1 – Arrival</h3>
                  <ul className="space-y-2 text-white/80">
                    <li>• Transfer from Airport to Resort in Srinagar</li>
                    <li>• Traditional welcome with Herbal Tea & Traditional Kashmiri Breads</li>
                    <li>• Lunch at Resort</li>
                    <li>• Pilgrim Induction</li>
                    <li>• One-on-One Introduction with Pilgrims</li>
                    <li>• Dinner at Resort</li>
                  </ul>
                </div>

                {/* Day 2 & 3 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 2 & 3 – A Sacred Pilgrimage to the Samkaropala (Shiva Sutra Rock)</h3>
                  <ul className="space-y-2 text-white/80 mb-4">
                    <li>• Breakfast at Resort</li>
                    <li>• Transfer to Fakir Gujri</li>
                    <li>• The Hike to Samkaropala will start from Fakir Gujri reaching halfway by ponies and then trekking to the Sacred site</li>
                    <li>• Packed Lunch will be served on the way to the Peak</li>
                    <li>• Base camp will be setup at the Lidwas Meadow where Pilgrims will stay overnight in shared camps</li>
                    <li>• Dinner at Base Camp</li>
                    <li>• Breakfast with Herbal Tea in the Morning while soaking in the picturesque views</li>
                    <li>• Trek towards the Sacred site</li>
                    <li>• Upon reaching the Sacred site Mediation and Satsang with Andrew Harvey at the Samkaropala</li>
                    <li>• Start the descend towards Base Camp</li>
                    <li>• Lunch at Base Camp</li>
                    <li>• Descend back to Fakir Gujri</li>
                    <li>• Transfer to the Resort</li>
                    <li>• Dinner at Resort</li>
                  </ul>
                  <div className="bg-black-700/50 rounded-lg p-4 border-l-4 border-gold-500">
                    <p className="text-white/70 italic text-sm">
                      Srikanthanatha, an incarnation of Lord Shiva, is said to have appeared in a dream to sage Vasugupta,
                      guiding him to a sacred rock on Mahadeva Mountain containing secret doctrines. Upon visiting the site,
                      the sage touched the rock, which turned over to reveal the Shiva Sutras engraved on it. This rock,
                      known as Samkaropala, has since been a revered spot for meditation, attracting sages and ascetics.
                    </p>
                  </div>
                </div>

                {/* Day 4 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 4 – A Day at Leisure in Srinagar</h3>
                  <ul className="space-y-2 text-white/80">
                    <li>• Breakfast at Resort</li>
                    <li>• Spend a leisurely morning at the resort</li>
                    <li>• Lunch at Resort</li>
                    <li>• Spend the day as you please exploring Srinagar and ask for recommendation on cafes, restaurants and markets from us</li>
                    <li>• Dinner at Resort</li>
                  </ul>
                </div>

                {/* Day 5 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 5 – Karma Cleansing & Healing with Conscious Communication</h3>
                  <ul className="space-y-2 text-white/80 mb-4">
                    <li>• Breakfast at Resort</li>
                    <li>• Satsang at Resort</li>
                    <li>• Lunch at Resort</li>
                    <li>• Cleansing & Healing with a Spiritual Healer (one-on-one)</li>
                    <li>• Storytelling amongst pilgrims around bon-fire in the evening at Resort</li>
                    <li>• Dinner at Resort</li>
                  </ul>
                  <div className="bg-black-700/50 rounded-lg p-4 border-l-4 border-gold-500">
                    <p className="text-white/70 italic text-sm">
                      Satsang is a Sanskrit term that translates to "being in the company of truth" It refers to a gathering
                      of individuals who come together to explore and discuss spiritual teachings, truths, and practices.
                    </p>
                  </div>
                </div>

                {/* Day 6 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 6 – Karma Cleansing & Healing with Conscious Communication</h3>
                  <ul className="space-y-2 text-white/80 mb-4">
                    <li>• Breakfast at Resort</li>
                    <li>• Satsang at Resort</li>
                    <li>• Lunch at Resort</li>
                    <li>• Visit Cheshm-e-Shahi (Spring of the Royals)</li>
                    <li>• Shikara Ride in Dal Lake at Sunset time</li>
                    <li>• Transfer to the Resort</li>
                    <li>• Storytelling amongst pilgrims around bon-fire in the evening at Resort</li>
                    <li>• Dinner at Resort</li>
                  </ul>
                  <div className="bg-black-700/50 rounded-lg p-4 border-l-4 border-gold-500">
                    <p className="text-white/70 italic text-sm">
                      The mineral water from the spring is believed to have healing properties and is often consumed by
                      visitors for its purity. The site has been associated with spiritual energy and tranquility,
                      attracting both tourists and locals seeking peace and rejuvenation.
                    </p>
                  </div>
                </div>

                {/* Day 7 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 7 – A walk through Kashmiri Shaivism</h3>
                  <ul className="space-y-2 text-white/80 mb-4">
                    <li>• Early Breakfast at Resort</li>
                    <li>• Visit Swami Lakshamnjoo Ashram</li>
                    <li>• Walk through the Ashram and Pray at Amriteshwara Temple at the Ashram</li>
                    <li>• Mughlai Lunch at Moti Mahal Restaurant near Ashram in Shalimar</li>
                    <li>• Briefing of the Kashmiri Shaivism and exhibition of Swami Lakshmanjoo's belongings</li>
                    <li>• Literary time at the Ashram Library (Dedicated to Kashmiri Shaivism)</li>
                    <li>• Transfer to the Resort in the evening</li>
                    <li>• Dinner at Resort</li>
                  </ul>
                  <div className="bg-black-700/50 rounded-lg p-4 border-l-4 border-gold-500">
                    <p className="text-white/70 italic text-sm">
                      Swami Lakshmanjoo (1907–1991) was a renowned master of Kashmiri Shaivism who dedicated his life to
                      preserving and sharing its teachings. Through his translations and insights, he made this ancient
                      philosophy accessible, emphasizing self-realization and divine consciousness.
                    </p>
                  </div>
                </div>

                {/* Day 8 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 8 – A Tapestry of Traditions</h3>
                  <ul className="space-y-2 text-white/80 mb-4">
                    <li>• Breakfast at Resort</li>
                    <li>• Introduction to the Kashmiri Arts & Handicrafts</li>
                    <li>• Lunch at Resort</li>
                    <li>• Experience the Artistry of Kashmir at Resort</li>
                    <li>• Artisan Meet-and-Greets</li>
                    <li>• Dinner at Resort</li>
                  </ul>
                  <div className="bg-black-700/50 rounded-lg p-4 border-l-4 border-gold-500">
                    <p className="text-white/70 italic text-sm">
                      Join us for an exclusive Kashmiri Art Exhibition, showcasing the region's rich craftsmanship,
                      such as Pashmina Shawls, Hand-Knotted Carpets, Papier-Mâché Art, Walnut Wood Carvings,
                      Jewelry and Intricate Embroidery. Engage directly with the creators to hear their stories.
                    </p>
                  </div>
                </div>

                {/* Day 9 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 9 – Spirit of Temples</h3>
                  <ul className="space-y-2 text-white/80 mb-4">
                    <li>• Breakfast at Resort</li>
                    <li>• Visit Kheer Bhawani Temple dedicated to Ragnya Devi – an incarnation of Goddess Durga</li>
                    <li>• Lunch at the banks of Sindh River at Wayil</li>
                    <li>• Visit Pandrethan Temple dedicated to Lord Shiva</li>
                    <li>• Visit Shankarachrya Temple dedicated to Lord Shiva</li>
                    <li>• Experience picturesque views of Srinagar Valley and River Jhelum at the Sunset time</li>
                    <li>• Transfer to Resort in the evening</li>
                    <li>• Dinner at Resort</li>
                  </ul>
                  <div className="bg-black-700/50 rounded-lg p-4 border-l-4 border-gold-500">
                    <p className="text-white/70 italic text-sm">
                      The Shankaracharya Temple sits atop the Takht-e-Suleiman hill at an elevation of 1,100 feet.
                      This ancient temple, dedicated to Lord Shiva, is believed to be the oldest place of worship in the Kashmir Valley.
                    </p>
                  </div>
                </div>

                {/* Day 10 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 10 – Radiant Health & Healing with Andrew Harvey</h3>
                  <ul className="space-y-2 text-white/80 mb-4">
                    <li>• Breakfast at Resort</li>
                    <li>• Meditation with Andrew Harvey</li>
                    <li>• Lunch at Resort</li>
                    <li>• Storytelling amongst pilgrims around bon-fire in the evening at Resort</li>
                    <li>• Dinner at Resort</li>
                  </ul>
                  <div className="bg-black-700/50 rounded-lg p-4 border-l-4 border-gold-500">
                    <p className="text-white/70 italic text-sm">
                      Clear out and heal from past Emotional, Physical and Mental pains and trauma to transform your life.
                    </p>
                  </div>
                </div>

                {/* Day 11 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 11 – A Mystic Day</h3>
                  <ul className="space-y-2 text-white/80 mb-4">
                    <li>• Breakfast at Resort</li>
                    <li>• Leave towards Anantnag and visit Lalleshwari Grave at Bijbehara</li>
                    <li>• Lunch on the way to Pahalgam</li>
                    <li>• Visit Martand Sun Temple Dedicated to Lord Surya</li>
                    <li>• Transfer to Pahalgam and take a rest with mountain views at the Resort</li>
                    <li>• Dinner at Resort</li>
                  </ul>
                  <div className="bg-black-700/50 rounded-lg p-4 border-l-4 border-gold-500">
                    <p className="text-white/70 italic text-sm">
                      Lalleshwari was a devotee of Lord Shiva and wrote many poems in Kashmiri, called "Vakhs".
                      The Martand Sun Temple is an 8th-century temple dedicated to the Sun God, Surya,
                      a masterpiece of Kashmiri architecture.
                    </p>
                  </div>
                </div>

                {/* Day 12 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 12 – A Sufi Day</h3>
                  <ul className="space-y-2 text-white/80 mb-4">
                    <li>• Breakfast at Resort</li>
                    <li>• Meditate at the stunning Tuliyan Valley Pass on the way back from Pahalgam</li>
                    <li>• Visit Aishmuqam Shrine</li>
                    <li>• Lunch at Anantnag</li>
                    <li>• Leave towards Srinagar and visit Badamwari (The Orchard of Almonds)</li>
                    <li>• Visit Akhand Mullah Shah Mosque</li>
                    <li>• Transfer to the Resort</li>
                    <li>• Dinner at Resort</li>
                  </ul>
                  <div className="bg-black-700/50 rounded-lg p-4 border-l-4 border-gold-500">
                    <p className="text-white/70 italic text-sm">
                      The Aishmuqam Shrine is dedicated to Sufi saint Baba Zain-ud-Din Rishi. Habba Khatoon,
                      the "Nightingale of Kashmir," was a 16th-century poetess whose love story with King Yousuf Shah Chak
                      began in Badamwari among blooming almond trees.
                    </p>
                  </div>
                </div>

                {/* Day 13 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 13 – Sufi Music Night</h3>
                  <ul className="space-y-2 text-white/80 mb-4">
                    <li>• Breakfast at Resort</li>
                    <li>• Meditation at Buddha Stupa in Harwan</li>
                    <li>• Transfer to Resort</li>
                    <li>• Lunch at Resort</li>
                    <li>• Soak in the essence of Sufi Music at Resort</li>
                    <li>• Dinner at Resort</li>
                  </ul>
                  <div className="bg-black-700/50 rounded-lg p-4 border-l-4 border-gold-500">
                    <p className="text-white/70 italic text-sm">
                      Experience the mystical allure of Kashmir with a Sufi Music Night. Enjoy soulful performances of
                      Kashmiri, Persian, and Urdu songs, inspired by great Sufi saints like Rumi, Hafiz, Laleshwari,
                      and Sheikh Noor-ud-din Wali. Accompanied by Santoor, Rabab, and Tabla.
                    </p>
                  </div>
                </div>

                {/* Day 14 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 14 – Oscar Night</h3>
                  <ul className="space-y-2 text-white/80 mb-4">
                    <li>• Breakfast at Resort</li>
                    <li>• Prepare for the Oscar Night</li>
                    <li>• Lunch at Resort</li>
                    <li>• Oscar Night of Spirituality</li>
                    <li>• Traditional Wazwan Dinner</li>
                  </ul>
                  <div className="bg-black-700/50 rounded-lg p-4 border-l-4 border-gold-500">
                    <p className="text-white/70 italic text-sm">
                      On the final evening of your spiritual journey, join us for Oscar Night of Spirituality.
                      This special event honors the transformative experiences and milestones of your spiritual journey.
                      Enjoy a memorable Wazwan feast featuring traditional Kashmiri dishes such as Kebab, Tabak Maaz,
                      Rista, Rogan Josh, Gushtaba, Paneer and Yakhni.
                    </p>
                  </div>
                </div>

                {/* Day 15 */}
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                  <h3 className="text-gold-500 font-bold text-xl mb-4">Day 15 – Farewell</h3>
                  <ul className="space-y-2 text-white/80">
                    <li>• Breakfast at Resort</li>
                    <li>• A Farewell at the Resort contemplating on the Sacred Journey</li>
                    <li>• Lunch at Resort</li>
                    <li>• Transfer to Airport</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Policies */}
        <section className="py-12 bg-black-700">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-8 text-center">
                <span className="text-gradient">Policies</span>
              </h2>

              <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                <ul className="space-y-3 text-white/80">
                  <li>• We are available 24x7 for your assistance.</li>
                  <li>• Travelers are requested to pay the booking amount 90 days prior to arrival.</li>
                  <li>• All bookings will be confirmed after the advance payment reflects in our bank account.</li>
                  <li>• In case of a government-imposed lockdown, your booking amount is safe with us and can be used in the future.</li>
                  <li>• We will not charge you as Tour operators for cancellations, but the Hotel Booking cancellations are subject to the Hotel Policies:</li>
                  <li className="ml-6">➢ Cancellation within 30 days of arrival is retained @ 100% with 0% refund.</li>
                  <li className="ml-6">➢ Cancellation within 30-60 days of arrival is retained @ 50% with 50% refund.</li>
                  <li className="ml-6">➢ Cancellation within 60-90 days of arrival is retained @ 0% with 100% refund.</li>
                  <li>• Kindly make the Advance Payments at least 90 days prior to arrival.</li>
                  <li>• In case the customer has to extend their stay due to any circumstances such as weather, lockdown or any other exigency, we will provide full assistance.</li>
                  <li>• As per Kashmir Tourism Advisory, all Visitors are advised to Hire Union Taxis for Sightseeing within the vicinities of Sonmarg, Pahalgam, Gulmarg & other destinations.</li>
                  <li>• Your Prepaid Sim cards will not work on your Trip to Kashmir & Ladakh, kindly arrange a Postpaid/Pre-on-Postpaid Sim.</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Payment Details */}
        <section className="py-12 bg-black-800">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-8 text-center">
                Payment <span className="text-gradient">Details</span>
              </h2>

              <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-gold-500 font-semibold mb-3">Bank Details</h3>
                    <div className="space-y-2 text-white/80">
                      <p><span className="text-white font-medium">Account Title:</span> Multi Destinations</p>
                      <p><span className="text-white font-medium">Account Number:</span> **************</p>
                      <p><span className="text-white font-medium">IFSC Code:</span> HDFC0008265</p>
                      <p><span className="text-white font-medium">Branch:</span> Lal Bazar, Srinagar, J&K</p>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-gold-500 font-semibold mb-3">UPI Payment</h3>
                    <div className="space-y-2 text-white/80">
                      <p><span className="text-white font-medium">UPI ID:</span> **********@ibl</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>

      <Footer />
    </div>
  );
}
