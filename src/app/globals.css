@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 3%;
    --foreground: 45 100% 60%;

    --card: 0 0% 5%;
    --card-foreground: 45 100% 60%;

    --popover: 0 0% 5%;
    --popover-foreground: 45 100% 60%;

    --primary: 45 100% 60%;
    --primary-foreground: 0 0% 3%;

    --secondary: 0 73% 31%;
    --secondary-foreground: 45 100% 96%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 60%;

    --accent: 12 76% 61%;
    --accent-foreground: 0 0% 3%;

    --destructive: 0 100% 40%;
    --destructive-foreground: 45 100% 60%;

    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 45 100% 60%;

    --radius: 0.5rem;

    /* Custom gradient card colors */
    --card-gold-start: 255, 215, 0;
    --card-gold-end: 255, 140, 0;
    --card-red-start: 220, 38, 38;
    --card-red-end: 153, 27, 27;
    --card-dark-start: 17, 24, 39;
    --card-dark-end: 3, 7, 18;

    /* Mobile viewport height fix */
    --vh: 1vh;
  }
}

@layer components {
  .text-gradient {
    @apply bg-gradient-to-r from-gold-400 to-gold-600 inline-block text-transparent bg-clip-text;
  }

  .cosmic-bg {
    position: relative;
  }

  .cosmic-bg::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/cosmic-bg.svg');
    background-size: cover;
    background-position: center;
    opacity: 0.05;
    z-index: -1;
  }

  .card-glow-hover {
    transition: all 0.3s ease;
  }

  .card-glow-hover:hover {
    box-shadow: 0 0 20px rgba(234, 179, 8, 0.2);
    transform: translateY(-5px);
  }

  .white-text {
    color: white;
  }

  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  }

  .hover-glow {
    transition: box-shadow 0.3s ease;
  }

  .hover-glow:hover {
    box-shadow: 0 0 15px rgba(234, 179, 8, 0.25);
  }

  .animate-pulse-slow {
    animation: pulse 6s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 0.5;
    }
    50% {
      opacity: 0.2;
    }
  }

  .animate-fade-in {
    animation: fadeIn 1s ease-in-out forwards;
    opacity: 0;
  }

  .animate-fade-in-up {
    animation: fadeInUp 1s ease-in-out forwards;
    opacity: 0;
    transform: translateY(20px);
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .delay-200 {
    animation-delay: 0.2s;
  }

  .delay-300 {
    animation-delay: 0.3s;
  }

  .delay-500 {
    animation-delay: 0.5s;
  }

  .bg-meditation {
    background-color: #000;
    background-image: radial-gradient(circle at 90% 10%, rgba(var(--card-red-start), 0.1), transparent 40%),
                      radial-gradient(circle at 10% 90%, rgba(111, 45, 189, 0.1), transparent 40%);
  }

  /* Flowing gradient animation for hero section */
  .flowing-gradient {
    background: linear-gradient(135deg, #000 0%, #11041b 100%);
    position: relative;
  }

  .flowing-gradient::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgb(48, 1, 106) 0%,
      rgba(255, 89, 0, 0.13) 50%,
      rgba(45, 1, 95, 0.595) 100%);
    filter: blur(80px);
    transform: translateZ(0);
    z-index: 0;
    animation: gradientFlow 15s ease infinite;
    background-size: 400% 400%;
  }

  @keyframes gradientFlow {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Dark orbs styling for hero section */
  .dark-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(50px);
    opacity: 0.2;
    animation: orb-float 20s ease-in-out infinite;
    background: rgba(0, 0, 0, 0.3);
  }

  .dark-orb.purple {
    background: radial-gradient(circle, rgba(128, 0, 128, 0.2) 0%, rgba(75, 0, 130, 0.1) 100%);
    animation-duration: 25s;
  }

  .dark-orb.blue {
    background: radial-gradient(circle, rgba(0, 0, 255, 0.15) 0%, rgba(0, 0, 139, 0.05) 100%);
    animation-duration: 30s;
    animation-delay: 3s;
  }

  .dark-orb.indigo {
    background: radial-gradient(circle, rgba(75, 0, 130, 0.15) 0%, rgba(138, 43, 226, 0.05) 100%);
    animation-duration: 27s;
    animation-delay: 1.5s;
  }

  @keyframes orb-float {
    0%, 100% {
      transform: translate(0, 0);
    }
    25% {
      transform: translate(5%, 5%);
    }
    50% {
      transform: translate(0, 10%);
    }
    75% {
      transform: translate(-5%, 5%);
    }
  }
}

/* Define min-height for main layout elements */
html, body {
  min-height: 100vh;
}

/* Mobile viewport height fix */
@media (max-width: 767px) {
  .min-h-screen {
    min-height: calc(var(--vh, 1vh) * 100);
    position: relative;
    z-index: 1;
  }
}

/* Layout class for creating full-height sections with header offset */
.layout-section {
  min-height: calc(100vh - 80px);
  padding-top: 80px;
}

main {
  flex: 1 0;
  padding-top: 0px; /* Ensure content doesn't overlap with fixed header */
}

/* Header specific styles for transparency issue */
header {
  will-change: background-color, padding;
  transition: background-color 0.3s ease, padding 0.3s ease, box-shadow 0.3s ease;
}

/* Transparent header at top */
header:not([class*="bg-black"]) {
  background-color: transparent !important;
}

/* Black header when scrolled */
header[class*="bg-black"] {
  background-color: #000 !important;
}

/* Force header box-shadow only when scrolled */
header:not([class*="shadow"]) {
  box-shadow: none !important;
}

header[class*="shadow"] {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4) !important;
}

/* Better prose styling for blog content */
.prose {
  font-size: 1.125rem;
  line-height: 1.75;
  width: 100% !important;
  max-width: 100% !important;
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  hyphens: auto;
}

@media (max-width: 640px) {
  .prose {
    font-size: 1rem;
    line-height: 1.6;
  }
}

.prose h1 {
  font-size: 2.25rem;
  line-height: 1.2;
  margin-top: 2em;
  margin-bottom: 0.8em;
  color: #fff;
  font-weight: 700;
  padding-top: 0.5em;
  margin-top: 1.5em;
  position: relative;
}

.prose h2 {
  font-size: 1.75rem;
  line-height: 1.3;
  margin-top: 1.75em;
  margin-bottom: 0.75em;
  color: #fff;
  font-weight: 600;
  padding-top: 0.5em;
  margin-top: 1.5em;
  position: relative;
}

.prose h3 {
  font-size: 1.5rem;
  line-height: 1.4;
  margin-top: 1.5em;
  margin-bottom: 0.7em;
  color: #fff;
  font-weight: 600;
  padding-top: 0.5em;
  margin-top: 1.5em;
  position: relative;
}

.prose h4 {
  font-size: 1.25rem;
  line-height: 1.5;
  margin-top: 1.25em;
  margin-bottom: 0.65em;
  color: #fff;
  font-weight: 600;
  padding-top: 0.5em;
  margin-top: 1.5em;
  position: relative;
}

.prose p {
  margin-bottom: 1.5em;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.85);
}

.prose a {
  color: rgba(234, 179, 8, 0.9);
  text-decoration: underline;
  text-underline-offset: 4px;
  transition: color 0.2s ease-in-out;
}

.prose a:hover {
  color: rgba(234, 179, 8, 1);
}

.prose ul, .prose ol {
  margin-left: 1.5em;
  margin-bottom: 1.5em;
  color: rgba(255, 255, 255, 0.85);
}

.prose li {
  margin-bottom: 0.5em;
}

.prose blockquote {
  font-style: italic;
  border-left: 4px solid rgba(234, 179, 8, 0.5);
  padding-left: 1em;
  margin: 1.5em 0;
  color: rgba(255, 255, 255, 0.7);
}

.prose pre {
  max-width: 100%;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 0.375rem;
  padding: 1rem;
  margin: 1.5em 0;
}

.prose code {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  font-size: 0.9em;
}

/* Fix for content overlapping in blog post */
.min-h-screen {
  position: relative;
  z-index: 1;
}

/* Header fixes */
header.fixed {
  position: fixed !important;
  z-index: 999 !important;
  width: 100%;
}

/* Force black background on scrolled header */
header[class*="bg-black"] {
  background-color: #000 !important;
}

/* Ensure article content is properly contained */
article {
  overflow: hidden;
  position: relative;
}

article img {
  max-width: 100% !important;
  height: auto !important;
  object-fit: contain;
}

/* Adjust content positioning in blog posts */
.prose {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

/* Fix content display issues */
.prose > * {
  margin-top: 1em;
  margin-bottom: 1em;
  max-width: 100%;
}

/* Additional fixes for media */
.prose img,
.prose video,
.prose iframe {
  max-width: 100% !important;
  height: auto !important;
  display: block;
  margin: 2em auto;
  border-radius: 0.5rem;
}

/* Add padding for the blog content to prevent overlapping with the header */
.blog-content {
  padding-top: 120px;
}

/* Apply word-break only where needed */
.break-words {
  word-break: break-word;
}

/* Fix for overflow issues */
main, article, section, div {
  overflow-wrap: break-word;
  word-wrap: break-word;
  max-width: 100vw;
  word-break: normal;
}
