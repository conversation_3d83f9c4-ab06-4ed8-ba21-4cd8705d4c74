"use client";

import React, { useState, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { packages } from "@/data/packagesData";
import Image from "next/image";
import { MapPin, Calendar, Users, Star, Search } from "lucide-react";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

export default function ExperiencesPage() {
  const [searchTerm, setSearchTerm] = useState("");

  // Filter packages based on search term
  const filteredPackages = useMemo(() => {
    if (!searchTerm) return packages;

    const term = searchTerm.toLowerCase();
    return packages.filter(pkg =>
      pkg.title.toLowerCase().includes(term) ||
      pkg.description.toLowerCase().includes(term)
    );
  }, [searchTerm]);

  return (
    <div className="min-h-screen flex flex-col bg-black-950">
      <Header />

      {/* Main content wrapper */}
      <div className="flex-grow flex flex-col">
        {/* Hero Banner */}
        <section className="py-16 mt-16 md:py-20 bg-black-600 bg-meditation cosmic-bg">
          <div className="absolute inset-0 z-0">
            <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-black-950 to-transparent" />
            <div className="absolute bottom-0 left-0 w-full h-64 bg-gradient-to-t from-black-950 to-transparent" />
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-3xl mx-auto text-center animate-fade-in">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                <span className="text-white">Our </span>
                <span className="text-gradient">Spiritual Experiences</span>
              </h1>
              <p className="text-lg text-white mb-8">
                Discover our carefully curated collection of spiritual journeys through the sacred lands of Kashmir.
              </p>

              {/* Search bar */}
              <div className="max-w-2xl mx-auto relative">
                <div className="relative">
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search experiences, destinations, or spiritual practices..."
                    className="w-full bg-black-900/60 border border-white/20 focus:border-gold-500/50 rounded-full px-5 py-3 pl-12 text-white placeholder-white/50 outline-none focus:ring-2 focus:ring-gold-500/30"
                  />
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/50 h-5 w-5" />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Content section */}
        <section className="py-12 md:py-16 bg-black-700 flex-1">
          <div className="container mx-auto px-4">
            {/* Results info */}
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-white font-medium">
                {filteredPackages.length} {filteredPackages.length === 1 ? 'experience' : 'experiences'} found
              </h2>
            </div>

            {/* Experiences grid */}
            {filteredPackages.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredPackages.map((pkg, index) => (
                  <div
                    key={index}
                    className="bg-black-900/40 backdrop-blur-sm border border-white/10 hover:border-gold-500/20 rounded-xl overflow-hidden transition-all duration-300 animate-fade-in-up hover-lift hover-glow"
                    style={{ animationDelay: `${0.1 + index * 0.05}s` }}
                  >
                    {/* Experience Image */}
                    <div className="relative h-60 overflow-hidden">
                      <Image
                        src={pkg.image}
                        alt={pkg.title}
                        width={500}
                        height={400}
                        className="object-cover w-full h-full transition-transform duration-500 hover:scale-110"
                        crossOrigin="anonymous"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent/30" />
                      <div className="absolute top-4 left-4 bg-gold-500/90 text-black-950 text-xs font-medium rounded-full px-3 py-1">
                        Spiritual
                      </div>
                     
                    </div>

                    {/* Experience Content */}
                    <div className="p-6">
                      <h3 className="text-white text-xl font-semibold mb-1">{pkg.title}</h3>
                      <p className="text-white/60 text-sm mb-4 line-clamp-6">{pkg.description}</p>

                  {/*     <div className="flex flex-col space-y-2 mb-4">
                        <div className="flex items-center text-white/80 text-sm">
                          <Calendar className="w-4 h-4 mr-2 text-gold-500" />
                          <span>Full Day Experience</span>
                        </div>
                        <div className="flex items-center text-white/80 text-sm">
                          <MapPin className="w-4 h-4 mr-2 text-gold-500" />
                          <span>Kashmir, India</span>
                        </div>
                        <div className="flex items-center text-white/80 text-sm">
                          <Users className="w-4 h-4 mr-2 text-gold-500" />
                          <span>Small groups (max 15)</span>
                        </div>
                      </div> */}

                    
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-16 bg-black-900/20 backdrop-blur-sm rounded-xl border border-white/10">
                <div className="text-white/40 text-5xl mb-4">🔍</div>
                <h3 className="text-white text-xl font-medium mb-2">No experiences found</h3>
                <p className="text-white/60 mb-6">Try adjusting your search to find what you're looking for.</p>
                <Button onClick={() => setSearchTerm("")} className="bg-gold-500 text-black-950 hover:bg-gold-600">
                  Clear Search
                </Button>
              </div>
            )}
          </div>
        </section>
      </div>

      <Footer />
    </div>
  );
}