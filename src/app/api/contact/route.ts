import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { z } from 'zod';

// Validation schema
const contactSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
});

export async function POST(request: NextRequest) {
  try {
    // Check if SMTP credentials are configured
    if (!process.env.SMTP_EMAIL || !process.env.SMTP_PASSWORD) {
      console.error('SMTP credentials not configured');
      return NextResponse.json(
        { error: 'Email service not configured' },
        { status: 500 }
      );
    }

    const body = await request.json();

    // Validate the request body
    const validatedData = contactSchema.parse(body);

    // Create transporter with more robust configuration for Vercel
    const transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_EMAIL,
        pass: process.env.SMTP_PASSWORD,
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Email content for the business
    const businessEmailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background: linear-gradient(135deg, #d4af37, #b8860b); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 28px;">New Contact Form Submission</h1>
          <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Wild World Wanderers</p>
        </div>
        
        <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="margin-bottom: 20px;">
            <h3 style="color: #333; margin: 0 0 10px 0; font-size: 18px; border-bottom: 2px solid #d4af37; padding-bottom: 5px;">Contact Details</h3>
            <p style="margin: 8px 0; color: #555;"><strong>Name:</strong> ${validatedData.name}</p>
            <p style="margin: 8px 0; color: #555;"><strong>Email:</strong> ${validatedData.email}</p>
            <p style="margin: 8px 0; color: #555;"><strong>Subject:</strong> ${validatedData.subject}</p>
          </div>
          
          <div>
            <h3 style="color: #333; margin: 0 0 10px 0; font-size: 18px; border-bottom: 2px solid #d4af37; padding-bottom: 5px;">Message</h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #d4af37;">
              <p style="margin: 0; color: #333; line-height: 1.6;">${validatedData.message.replace(/\n/g, '<br>')}</p>
            </div>
          </div>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
            <p style="margin: 0; color: #888; font-size: 14px;">
              This message was sent from the Wild World Wanderers contact form<br>
              <strong>Timestamp:</strong> ${new Date().toLocaleString()}
            </p>
          </div>
        </div>
      </div>
    `;

    // Email content for the customer (auto-reply)
    const customerEmailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background: linear-gradient(135deg, #d4af37, #b8860b); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Thank You for Contacting Us!</h1>
          <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Wild World Wanderers</p>
        </div>
        
        <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <p style="color: #333; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Dear ${validatedData.name},
          </p>
          
          <p style="color: #333; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Thank you for reaching out to Wild World Wanderers! We have received your message and truly appreciate your interest in our spiritual journeys through the sacred landscapes of Kashmir.
          </p>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 4px solid #d4af37; margin: 20px 0;">
            <h3 style="color: #333; margin: 0 0 10px 0;">Your Message Summary:</h3>
            <p style="margin: 5px 0; color: #555;"><strong>Subject:</strong> ${validatedData.subject}</p>
            <p style="margin: 5px 0; color: #555;"><strong>Message:</strong> ${validatedData.message}</p>
          </div>
          
          <p style="color: #333; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Our team will review your inquiry and respond within 24-48 hours. In the meantime, feel free to explore our website to learn more about our transformative spiritual experiences.
          </p>
          
          <div style="background: linear-gradient(135deg, #d4af37, #b8860b); padding: 20px; border-radius: 5px; text-align: center; margin: 20px 0;">
            <h3 style="color: white; margin: 0 0 10px 0;">Contact Information</h3>
            <p style="color: white; margin: 5px 0; opacity: 0.9;">📧 <EMAIL></p>
            <p style="color: white; margin: 5px 0; opacity: 0.9;">📞 9419955663</p>
            <p style="color: white; margin: 5px 0; opacity: 0.9;">📍 1st Floor, Pulloo Complex, Munawara Abad, Srinagar, J&K 190001</p>
          </div>
          
          <p style="color: #333; font-size: 16px; line-height: 1.6; margin-bottom: 0;">
            With gratitude and blessings,<br>
            <strong>The Wild World Wanderers Team</strong>
          </p>
        </div>
      </div>
    `;

    // Verify transporter connection (optional, for debugging)
    try {
      await transporter.verify();
      console.log('SMTP connection verified successfully');
    } catch (verifyError) {
      console.error('SMTP verification failed:', verifyError);
      // Continue anyway, as verify() can sometimes fail even when sending works
    }

    // Send email to business
    console.log('Sending business email...');
    const businessEmailResult = await transporter.sendMail({
      from: process.env.SMTP_EMAIL,
      to: '<EMAIL>',
      subject: `New Contact Form: ${validatedData.subject}`,
      html: businessEmailContent,
      replyTo: validatedData.email,
    });
    console.log('Business email sent:', businessEmailResult.messageId);

    // Send auto-reply to customer
    console.log('Sending customer auto-reply...');
    const customerEmailResult = await transporter.sendMail({
      from: process.env.SMTP_EMAIL,
      to: validatedData.email,
      subject: 'Thank you for contacting Wild World Wanderers',
      html: customerEmailContent,
    });
    console.log('Customer email sent:', customerEmailResult.messageId);

    return NextResponse.json(
      {
        message: 'Email sent successfully',
        businessEmailId: businessEmailResult.messageId,
        customerEmailId: customerEmailResult.messageId
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Email sending error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    // More detailed error logging for debugging
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    return NextResponse.json(
      { error: 'Failed to send email. Please try again later.' },
      { status: 500 }
    );
  }
}
