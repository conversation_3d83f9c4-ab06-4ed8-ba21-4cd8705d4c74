"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { packages } from "@/data/packagesData";
import Image from "next/image";
import { MapPin, Calendar, Users, Star } from "lucide-react";

export default function PackagesSection() {
  // Display only the first 3 packages on the homepage
  const featuredPackages = packages.slice(0, 3);

  return (
    <section id="packages" className="py-16 md:py-20 bg-black-950 relative overflow-hidden cosmic-bg">
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-black-600 to-transparent" />
        <div className="absolute bottom-0 left-0 w-full h-64 bg-gradient-to-t from-black-900 to-transparent" />
        <div className="absolute top-1/3 right-1/4 w-64 h-64 rounded-full bg-red-500/5 blur-3xl animate-pulse-slow" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center max-w-3xl mx-auto mb-12 animate-fade-in">
          <div className="inline-block mb-3 px-3 py-1 bg-gradient-to-r from-gold-500/20 to-red-500/20 rounded-full">
            <span className="text-white text-sm font-medium">Spiritual Journeys</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="text-white">Featured </span>
            <span className="text-gradient">Experiences</span>
          </h2>
          <p className="text-lg text-white">
            Experience meticulously crafted spiritual journeys through the mystical landscapes of Kashmir.
            Each experience is thoughtfully designed to offer profound spiritual practices and cultural immersion.
          </p>
        </div>

        {/* Featured Experiences Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {featuredPackages.map((pkg, index) => (
            <div
              key={index}
              className="bg-black-500/40 backdrop-blur-sm border border-white/10 hover:border-gold-500/20 rounded-xl overflow-hidden transition-all duration-300 animate-fade-in-up hover-lift hover-glow"
              style={{ animationDelay: `${0.2 + index * 0.1}s` }}
            >
              {/* Experience Image */}
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={pkg.image}
                  alt={pkg.title}
                  width={600}
                  height={400}
                  className="object-cover w-full h-full transition-transform duration-500 hover:scale-110"
                  crossOrigin="anonymous"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent/30" />
                <div className="absolute top-4 left-4 bg-gold-500/90 text-black-950 text-xs font-medium rounded-full px-3 py-1">
                  Spiritual
                </div>
                <div className="absolute bottom-4 right-4 flex items-center bg-black/60 text-white text-xs font-medium rounded-full px-2 py-1">
                  <Star className="w-3 h-3 mr-1 text-gold-500" fill="currentColor" />
                  <span>4.9</span>
                </div>
              </div>

              {/* Experience Content */}
              <div className="p-6">
                <h3 className="text-white text-xl font-semibold mb-1">{pkg.title}</h3>
                <p className="text-white/60 text-sm mb-4 line-clamp-4">{pkg.description}</p>

          
              </div>
            </div>
          ))}
        </div>

        {/* View All Experiences Button */}
        <div className="text-center mt-10 animate-fade-in delay-500">
          <Link href="/experiences">
            <Button
              size="lg"
              className="bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black-950 font-medium px-8 py-6 rounded-full shadow-lg transition-all duration-300 hover:shadow-gold-500/20 hover:shadow-xl hover-lift"
            >
              View All Experiences
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
