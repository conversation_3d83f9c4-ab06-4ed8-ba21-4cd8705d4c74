{"name": "spiritual-meditation", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@lottiefiles/react-lottie-player": "^3.6.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.6.2", "lucide-react": "^0.475.0", "next": "^15.2.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "^20.17.28", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.5", "eslint": "^9.23.0", "eslint-config-next": "15.1.7", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.2"}}