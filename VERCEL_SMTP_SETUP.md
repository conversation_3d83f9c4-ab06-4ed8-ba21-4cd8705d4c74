# Vercel SMTP Setup Guide

This guide explains how to configure SMTP email functionality for the Wild World Wanderers website when deployed on Vercel.

## Prerequisites

1. A Gmail account for sending emails
2. Vercel account and project deployed
3. Access to Vercel dashboard

## Step 1: Configure Gmail for SMTP

### Enable 2-Factor Authentication
1. Go to your Google Account settings
2. Navigate to "Security"
3. Enable "2-Step Verification" if not already enabled

### Generate App Password
1. In Google Account settings, go to "Security"
2. Under "2-Step Verification", click on "App passwords"
3. Select "Mail" as the app and "Other" as the device
4. Enter "Wild World Wanderers Website" as the device name
5. Click "Generate"
6. Copy the 16-character app password (save it securely)

## Step 2: Configure Vercel Environment Variables

### Via Vercel Dashboard
1. Go to your Vercel project dashboard
2. Navigate to "Settings" → "Environment Variables"
3. Add the following variables:

```
SMTP_EMAIL=<EMAIL>
SMTP_PASSWORD=your_16_character_app_password_here
```

### Via Vercel CLI
```bash
vercel env add SMTP_EMAIL
# Enter: <EMAIL>

vercel env add SMTP_PASSWORD
# Enter: your_16_character_app_password_here
```

## Step 3: Deploy and Test

### Redeploy Your Application
After adding environment variables, redeploy your application:
```bash
vercel --prod
```

### Test the Contact Form
1. Visit your deployed website
2. Navigate to the contact form (homepage or /contact page)
3. Fill out and submit the form
4. Check for success/error messages
5. Verify emails are received

## Step 4: Monitoring and Debugging

### Check Vercel Function Logs
1. Go to Vercel dashboard → "Functions" tab
2. Click on the `/api/contact` function
3. View logs for any errors or success messages

### Common Issues and Solutions

#### Issue: "Email service not configured"
- **Cause**: Environment variables not set
- **Solution**: Verify SMTP_EMAIL and SMTP_PASSWORD are set in Vercel

#### Issue: "Authentication failed"
- **Cause**: Incorrect app password or 2FA not enabled
- **Solution**: Regenerate app password and ensure 2FA is enabled

#### Issue: "Connection timeout"
- **Cause**: Network issues or Gmail blocking
- **Solution**: Check Gmail security settings and allow less secure apps if needed

#### Issue: Emails not being received
- **Cause**: Emails might be in spam folder
- **Solution**: Check spam folder and add sender to whitelist

## Step 5: Production Considerations

### Email Rate Limits
- Gmail has sending limits (500 emails/day for free accounts)
- Consider upgrading to Google Workspace for higher limits
- Implement rate limiting in your API if needed

### Alternative SMTP Providers
For production use, consider these alternatives:
- **SendGrid**: More reliable for transactional emails
- **Mailgun**: Good for high-volume sending
- **Amazon SES**: Cost-effective for AWS users
- **Resend**: Modern email API with good developer experience

### Security Best Practices
1. Never commit SMTP credentials to version control
2. Use environment variables for all sensitive data
3. Implement rate limiting to prevent abuse
4. Add CAPTCHA for additional security if needed

## Step 6: Testing Checklist

- [ ] Environment variables set in Vercel
- [ ] Gmail app password generated and working
- [ ] Contact form submits without errors
- [ ] Business email <NAME_EMAIL>
- [ ] Customer auto-reply email received
- [ ] Form validation working correctly
- [ ] Toast notifications appearing
- [ ] Form resets after successful submission

## Troubleshooting Commands

### Check Environment Variables
```bash
vercel env ls
```

### View Function Logs
```bash
vercel logs --follow
```

### Test API Endpoint Directly
```bash
curl -X POST https://your-domain.vercel.app/api/contact \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "subject": "Test Subject",
    "message": "This is a test message"
  }'
```

## Support

If you encounter issues:
1. Check Vercel function logs
2. Verify Gmail settings and app password
3. Test with a simple email first
4. Contact support if problems persist

## Email Templates

The system sends two emails:
1. **Business Email**: Notification to Wild World Wanderers team
2. **Customer Email**: Auto-reply confirmation to the customer

Both emails are professionally formatted with company branding and include all necessary contact information.
